// src/core/material/material.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Material system

#pragma once

#include "../math/vec3.hpp"
#include "../scene/scene.hpp"
#include <memory>
#include <string>

namespace photon {

// Forward declarations
class Sampler;
struct Intersection;

/**
 * @brief BSDF sample structure
 */
struct BSDFSample {
    Color3 f;           ///< BSDF value
    Vec3 wi;            ///< Incident direction (towards surface)
    float pdf;          ///< Probability density function
    bool isDelta;       ///< Is delta distribution (perfect reflection/transmission)
    
    BSDFSample() : f(0), wi(0), pdf(0), isDelta(false) {}
    BSDFSample(const Color3& f, const Vec3& wi, float pdf, bool isDelta = false)
        : f(f), wi(wi), pdf(pdf), isDelta(isDelta) {}
    
    bool isValid() const { return pdf > 0.0f && !f.isZero(); }
};

/**
 * @brief Abstract base material class
 */
class Material {
public:
    Material() = default;
    virtual ~Material() = default;
    
    // Non-copyable
    Material(const Material&) = delete;
    Material& operator=(const Material&) = delete;
    
    /**
     * @brief Evaluate BSDF
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction (from surface)
     * @param wi Incident direction (towards surface)
     * @return BSDF value
     */
    virtual Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const = 0;
    
    /**
     * @brief Sample BSDF direction
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction (from surface)
     * @param sampler Random sampler
     * @return BSDF sample
     */
    virtual BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const = 0;
    
    /**
     * @brief Get BSDF PDF for given directions
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction (from surface)
     * @param wi Incident direction (towards surface)
     * @return Probability density
     */
    virtual float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const = 0;
    
    /**
     * @brief Get emitted radiance (for emissive materials)
     * 
     * @param isect Intersection information
     * @param wo Outgoing direction
     * @return Emitted radiance
     */
    virtual Color3 Le(const Intersection& isect, const Vec3& wo) const { return Color3(0); }
    
    /**
     * @brief Check if material is emissive
     */
    virtual bool isEmissive() const { return false; }
    
    /**
     * @brief Get material name
     */
    virtual std::string getName() const = 0;
};

/**
 * @brief Lambertian diffuse material
 */
class DiffuseMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param albedo Diffuse reflectance
     */
    DiffuseMaterial(const Color3& albedo = Color3(0.8f));
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    std::string getName() const override { return "Diffuse"; }
    
    /**
     * @brief Set albedo
     */
    void setAlbedo(const Color3& albedo) { m_albedo = albedo; }
    
    /**
     * @brief Get albedo
     */
    const Color3& getAlbedo() const { return m_albedo; }

private:
    Color3 m_albedo;
};

/**
 * @brief Perfect mirror material
 */
class MirrorMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param reflectance Mirror reflectance
     */
    MirrorMaterial(const Color3& reflectance = Color3(0.9f));
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    std::string getName() const override { return "Mirror"; }

private:
    Color3 m_reflectance;
};

/**
 * @brief Emissive material (area light)
 */
class EmissiveMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param emission Emitted radiance
     */
    EmissiveMaterial(const Color3& emission = Color3(1.0f));
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    Color3 Le(const Intersection& isect, const Vec3& wo) const override;
    bool isEmissive() const override { return true; }
    std::string getName() const override { return "Emissive"; }
    
    /**
     * @brief Set emission
     */
    void setEmission(const Color3& emission) { m_emission = emission; }
    
    /**
     * @brief Get emission
     */
    const Color3& getEmission() const { return m_emission; }

private:
    Color3 m_emission;
};

/**
 * @brief Plastic material (diffuse + specular)
 */
class PlasticMaterial : public Material {
public:
    /**
     * @brief Constructor
     * 
     * @param diffuse Diffuse component
     * @param specular Specular component
     * @param roughness Surface roughness [0,1]
     */
    PlasticMaterial(const Color3& diffuse = Color3(0.5f), 
                   const Color3& specular = Color3(0.1f),
                   float roughness = 0.1f);
    
    Color3 f(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    BSDFSample sample(const Intersection& isect, const Vec3& wo, Sampler& sampler) const override;
    float pdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const override;
    std::string getName() const override { return "Plastic"; }

private:
    Color3 m_diffuse;
    Color3 m_specular;
    float m_roughness;
    
    /**
     * @brief Fresnel reflectance
     */
    float fresnel(float cosTheta) const;
    
    /**
     * @brief Microfacet distribution
     */
    float distribution(const Vec3& wh, float alpha) const;
    
    /**
     * @brief Masking-shadowing function
     */
    float geometry(const Vec3& wo, const Vec3& wi, const Vec3& wh, float alpha) const;
};

} // namespace photon
