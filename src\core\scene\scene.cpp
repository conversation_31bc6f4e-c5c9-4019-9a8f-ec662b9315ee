// src/core/scene/scene.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Scene management implementation

#include "scene.hpp"
#include "../common.hpp"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <limits>

namespace photon {

// Mesh implementation
Mesh::Mesh(const std::string& meshName) : name(meshName) {
    // Initialize empty mesh
}

void Mesh::addTriangle(const Point3& v0, const Point3& v1, const Point3& v2,
                      const Normal3& n0, const Normal3& n1, const Normal3& n2,
                      const Vec3& uv0, const Vec3& uv1, const Vec3& uv2) {
    uint32_t baseIndex = static_cast<uint32_t>(vertices.size());
    
    // Add vertices
    vertices.push_back(v0);
    vertices.push_back(v1);
    vertices.push_back(v2);
    
    // Add normals if provided
    if (n0.lengthSquared() > 0 || n1.lengthSquared() > 0 || n2.lengthSquared() > 0) {
        // Ensure normals vector has correct size
        while (normals.size() < vertices.size() - 3) {
            normals.push_back(Normal3(0, 1, 0)); // Default up normal
        }
        normals.push_back(n0.lengthSquared() > 0 ? n0.normalized() : Normal3(0, 1, 0));
        normals.push_back(n1.lengthSquared() > 0 ? n1.normalized() : Normal3(0, 1, 0));
        normals.push_back(n2.lengthSquared() > 0 ? n2.normalized() : Normal3(0, 1, 0));
    }
    
    // Add UVs if provided
    if (uv0.lengthSquared() > 0 || uv1.lengthSquared() > 0 || uv2.lengthSquared() > 0) {
        // Ensure UVs vector has correct size
        while (uvs.size() < vertices.size() - 3) {
            uvs.push_back(Vec3(0, 0, 0)); // Default UV
        }
        uvs.push_back(uv0);
        uvs.push_back(uv1);
        uvs.push_back(uv2);
    }
    
    // Add indices
    indices.push_back(baseIndex);
    indices.push_back(baseIndex + 1);
    indices.push_back(baseIndex + 2);
}

void Mesh::addQuad(const Point3& v0, const Point3& v1, const Point3& v2, const Point3& v3,
                  const Normal3& n0, const Normal3& n1, const Normal3& n2, const Normal3& n3,
                  const Vec3& uv0, const Vec3& uv1, const Vec3& uv2, const Vec3& uv3) {
    // Split quad into two triangles: (v0,v1,v2) and (v0,v2,v3)
    addTriangle(v0, v1, v2, n0, n1, n2, uv0, uv1, uv2);
    addTriangle(v0, v2, v3, n0, n2, n3, uv0, uv2, uv3);
}

void Mesh::computeNormals() {
    if (vertices.empty() || indices.empty()) return;
    
    // Initialize normals to zero
    normals.clear();
    normals.resize(vertices.size(), Normal3(0));
    
    // Accumulate face normals
    for (size_t i = 0; i < indices.size(); i += 3) {
        uint32_t i0 = indices[i];
        uint32_t i1 = indices[i + 1];
        uint32_t i2 = indices[i + 2];
        
        if (i0 >= vertices.size() || i1 >= vertices.size() || i2 >= vertices.size()) {
            continue; // Skip invalid indices
        }
        
        const Point3& v0 = vertices[i0];
        const Point3& v1 = vertices[i1];
        const Point3& v2 = vertices[i2];
        
        // Compute face normal
        Vec3 edge1 = v1 - v0;
        Vec3 edge2 = v2 - v0;
        Normal3 faceNormal = edge1.cross(edge2).normalized();
        
        // Accumulate to vertex normals
        normals[i0] += faceNormal;
        normals[i1] += faceNormal;
        normals[i2] += faceNormal;
    }
    
    // Normalize vertex normals
    for (auto& normal : normals) {
        if (normal.lengthSquared() > 0) {
            normal.normalize();
        } else {
            normal = Normal3(0, 1, 0); // Default up normal
        }
    }
}

bool Mesh::isValid() const {
    if (vertices.empty() || indices.empty()) return false;
    if (indices.size() % 3 != 0) return false;
    
    // Check indices are valid
    for (uint32_t index : indices) {
        if (index >= vertices.size()) return false;
    }
    
    // Check normals size if present
    if (!normals.empty() && normals.size() != vertices.size()) return false;
    
    // Check UVs size if present
    if (!uvs.empty() && uvs.size() != vertices.size()) return false;
    
    return true;
}

// Scene implementation
Scene::Scene() {
    m_bounds.min = Point3(std::numeric_limits<float>::max());
    m_bounds.max = Point3(std::numeric_limits<float>::lowest());
}

Scene::~Scene() {
    clear();
}

void Scene::addMesh(std::shared_ptr<Mesh> mesh) {
    if (!mesh || !mesh->isValid()) {
        std::cerr << "Warning: Attempted to add invalid mesh to scene" << std::endl;
        return;
    }
    
    m_meshes.push_back(mesh);
    m_accelerationBuilt = false;
    updateBounds();
}

void Scene::addMaterial(const std::string& name, std::shared_ptr<Material> material) {
    if (!material) {
        std::cerr << "Warning: Attempted to add null material: " << name << std::endl;
        return;
    }
    
    m_materials[name] = material;
}

void Scene::addLight(std::shared_ptr<Light> light) {
    if (!light) {
        std::cerr << "Warning: Attempted to add null light to scene" << std::endl;
        return;
    }
    
    m_lights.push_back(light);
}

std::shared_ptr<Material> Scene::getMaterial(const std::string& name) const {
    auto it = m_materials.find(name);
    return (it != m_materials.end()) ? it->second : nullptr;
}

bool Scene::intersect(const Ray& ray, Intersection& isect) const {
    if (!m_embreeScene) {
        std::cerr << "Error: Embree scene not built" << std::endl;
        return false;
    }
    
    // Create Embree ray
    RTCRayHit rayhit;
    rayhit.ray.org_x = ray.o.x;
    rayhit.ray.org_y = ray.o.y;
    rayhit.ray.org_z = ray.o.z;
    rayhit.ray.dir_x = ray.d.x;
    rayhit.ray.dir_y = ray.d.y;
    rayhit.ray.dir_z = ray.d.z;
    rayhit.ray.tnear = ray.tMin;
    rayhit.ray.tfar = ray.tMax;
    rayhit.ray.mask = 0xFFFFFFFF;
    rayhit.ray.flags = 0;
    rayhit.hit.geomID = RTC_INVALID_GEOMETRY_ID;
    rayhit.hit.instID[0] = RTC_INVALID_GEOMETRY_ID;
    
    // Perform intersection
    rtcIntersect1(m_embreeScene, &rayhit);
    
    if (rayhit.hit.geomID == RTC_INVALID_GEOMETRY_ID) {
        isect.hit = false;
        return false;
    }
    
    // Fill intersection data
    isect.hit = true;
    isect.t = rayhit.ray.tfar;
    isect.p = ray.at(isect.t);
    isect.n = Normal3(rayhit.hit.Ng_x, rayhit.hit.Ng_y, rayhit.hit.Ng_z).normalized();
    isect.u = rayhit.hit.u;
    isect.v = rayhit.hit.v;
    isect.primitiveId = rayhit.hit.primID;
    isect.geometryId = rayhit.hit.geomID;
    
    // Get material from mesh
    if (isect.geometryId < m_meshes.size()) {
        isect.material = m_meshes[isect.geometryId]->material;
    }
    
    return true;
}

bool Scene::intersectShadow(const Ray& ray) const {
    if (!m_embreeScene) return false;
    
    // Create Embree ray for occlusion test
    RTCRay rtcRay;
    rtcRay.org_x = ray.o.x;
    rtcRay.org_y = ray.o.y;
    rtcRay.org_z = ray.o.z;
    rtcRay.dir_x = ray.d.x;
    rtcRay.dir_y = ray.d.y;
    rtcRay.dir_z = ray.d.z;
    rtcRay.tnear = ray.tMin;
    rtcRay.tfar = ray.tMax;
    rtcRay.mask = 0xFFFFFFFF;
    rtcRay.flags = 0;
    
    // Perform occlusion test
    rtcOccluded1(m_embreeScene, &rtcRay);
    
    return rtcRay.tfar < 0.0f; // Ray was occluded
}

Scene::Bounds Scene::getBounds() const {
    return m_bounds;
}

void Scene::updateBounds() {
    m_bounds.min = Point3(std::numeric_limits<float>::max());
    m_bounds.max = Point3(std::numeric_limits<float>::lowest());
    
    for (const auto& mesh : m_meshes) {
        for (const auto& vertex : mesh->vertices) {
            m_bounds.min = m_bounds.min.min(vertex);
            m_bounds.max = m_bounds.max.max(vertex);
        }
    }
    
    // Handle empty scene
    if (m_meshes.empty()) {
        m_bounds.min = Point3(0);
        m_bounds.max = Point3(0);
    }
}

void Scene::clear() {
    m_meshes.clear();
    m_materials.clear();
    m_lights.clear();
    
    if (m_embreeScene) {
        rtcReleaseScene(m_embreeScene);
        m_embreeScene = nullptr;
    }
    
    m_accelerationBuilt = false;
    updateBounds();
}

Scene::Statistics Scene::getStatistics() const {
    Statistics stats;
    stats.meshCount = m_meshes.size();
    stats.materialCount = m_materials.size();
    stats.lightCount = m_lights.size();

    for (const auto& mesh : m_meshes) {
        stats.triangleCount += mesh->getTriangleCount();
        stats.vertexCount += mesh->getVertexCount();
    }

    return stats;
}

void Scene::buildAccelerationStructure(RTCDevice device) {
    if (m_embreeScene) {
        rtcReleaseScene(m_embreeScene);
    }

    m_embreeScene = rtcNewScene(device);

    // Add geometry to Embree scene
    for (size_t i = 0; i < m_meshes.size(); ++i) {
        auto& mesh = m_meshes[i];

        RTCGeometry geom = rtcNewGeometry(device, RTC_GEOMETRY_TYPE_TRIANGLE);

        // Set vertex buffer
        float* vertices = (float*)rtcSetNewGeometryBuffer(geom, RTC_BUFFER_TYPE_VERTEX, 0,
            RTC_FORMAT_FLOAT3, 3 * sizeof(float), mesh->vertices.size());

        for (size_t j = 0; j < mesh->vertices.size(); ++j) {
            vertices[j * 3 + 0] = mesh->vertices[j].x;
            vertices[j * 3 + 1] = mesh->vertices[j].y;
            vertices[j * 3 + 2] = mesh->vertices[j].z;
        }

        // Set index buffer
        unsigned* indices = (unsigned*)rtcSetNewGeometryBuffer(geom, RTC_BUFFER_TYPE_INDEX, 0,
            RTC_FORMAT_UINT3, 3 * sizeof(unsigned), mesh->indices.size() / 3);

        for (size_t j = 0; j < mesh->indices.size(); ++j) {
            indices[j] = mesh->indices[j];
        }

        rtcCommitGeometry(geom);
        mesh->geometryId = rtcAttachGeometry(m_embreeScene, geom);
        rtcReleaseGeometry(geom);
    }

    rtcCommitScene(m_embreeScene);
    m_accelerationBuilt = true;
}

bool Scene::loadFromFile(const std::string& filename) {
    // TODO: Implement scene loading from JSON/other formats
    std::cerr << "Scene loading not yet implemented: " << filename << std::endl;
    return false;
}

bool Scene::saveToFile(const std::string& filename) const {
    // TODO: Implement scene saving
    std::cerr << "Scene saving not yet implemented: " << filename << std::endl;
    return false;
}

} // namespace photon
