# PhotonRender - Simplified CMake Configuration
# This configuration builds only the core components without <PERSON><PERSON><PERSON>
# for testing and development purposes

cmake_minimum_required(VERSION 3.20)
project(PhotonRender VERSION 1.0.0 LANGUAGES CXX)

# C++ Standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Options
option(BUILD_TESTS "Build test suite" ON)
option(BUILD_SIMPLE "Build simplified version without Embree" ON)

# Compiler flags
if(MSVC)
    add_compile_options(/W4 /permissive-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../src)

# Core source files (minimal set without <PERSON><PERSON><PERSON> dependencies)
set(CORE_SOURCES
    # Math library only
    ${CMAKE_CURRENT_SOURCE_DIR}/../src/core/math/matrix4.cpp

    # Image I/O only
    ${CMAKE_CURRENT_SOURCE_DIR}/../src/core/image/image_io.cpp

    # Samplers only
    ${CMAKE_CURRENT_SOURCE_DIR}/../src/core/sampler/sampler.cpp

    # Test framework only
    ${CMAKE_CURRENT_SOURCE_DIR}/../src/core/test/mock_renderer.cpp
)

# Create simplified core library
add_library(photon_core_simple STATIC ${CORE_SOURCES})

# Set target properties
set_target_properties(photon_core_simple PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# Include directories for the library
target_include_directories(photon_core_simple
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_SOURCE_DIR}/../src
)

# Preprocessor definitions for simplified build
target_compile_definitions(photon_core_simple
    PUBLIC
        PHOTON_SIMPLE_BUILD
        PHOTON_USE_SIMPLE_COMMON
        PHOTON_VERSION_MAJOR=1
        PHOTON_VERSION_MINOR=0
        PHOTON_VERSION_PATCH=0
)

# Find and link STB (for image I/O)
find_path(STB_INCLUDE_DIR stb_image.h PATHS
    ${CMAKE_CURRENT_SOURCE_DIR}/../external/stb
    ${CMAKE_CURRENT_SOURCE_DIR}/../third_party/stb
    ${CMAKE_CURRENT_SOURCE_DIR}/../build/_deps/stb-src
    /usr/include/stb
    /usr/local/include/stb
)

if(STB_INCLUDE_DIR)
    target_include_directories(photon_core_simple PUBLIC ${STB_INCLUDE_DIR})
    target_compile_definitions(photon_core_simple PUBLIC PHOTON_HAS_STB)
    message(STATUS "STB found: ${STB_INCLUDE_DIR}")
else()
    message(WARNING "STB not found - image I/O will be limited")
endif()

# Find OpenMP (optional)
find_package(OpenMP)
if(OpenMP_CXX_FOUND)
    target_link_libraries(photon_core_simple PUBLIC OpenMP::OpenMP_CXX)
    target_compile_definitions(photon_core_simple PUBLIC PHOTON_HAS_OPENMP)
    message(STATUS "OpenMP found and enabled")
endif()

# Test executable
if(BUILD_TESTS)
    add_executable(photon_test_simple ${CMAKE_CURRENT_SOURCE_DIR}/../src/main_simple.cpp)
    target_link_libraries(photon_test_simple photon_core_simple)
    
    # Set output directory
    set_target_properties(photon_test_simple PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    )
endif()

# Installation
install(TARGETS photon_core_simple
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/ DESTINATION include)

# Status messages
message(STATUS "")
message(STATUS "PhotonRender Simplified Build ${PROJECT_VERSION}")
message(STATUS "")
message(STATUS "Build type:        ${CMAKE_BUILD_TYPE}")
message(STATUS "Simple build:      ${BUILD_SIMPLE}")
message(STATUS "Tests:             ${BUILD_TESTS}")
message(STATUS "OpenMP support:    ${OpenMP_CXX_FOUND}")
message(STATUS "STB support:       ${STB_INCLUDE_DIR}")
message(STATUS "")

# Create build script for Windows
if(WIN32)
    file(WRITE ${CMAKE_BINARY_DIR}/build_simple.bat
        "@echo off\n"
        "echo Building PhotonRender Simplified...\n"
        "cmake --build . --config Release --target photon_test_simple\n"
        "if %ERRORLEVEL% EQU 0 (\n"
        "    echo Build successful!\n"
        "    echo Running tests...\n"
        "    bin\\Release\\photon_test_simple.exe\n"
        ") else (\n"
        "    echo Build failed!\n"
        ")\n"
        "pause\n"
    )
endif()

# Create build script for Unix
if(UNIX)
    file(WRITE ${CMAKE_BINARY_DIR}/build_simple.sh
        "#!/bin/bash\n"
        "echo \"Building PhotonRender Simplified...\"\n"
        "cmake --build . --config Release --target photon_test_simple\n"
        "if [ $? -eq 0 ]; then\n"
        "    echo \"Build successful!\"\n"
        "    echo \"Running tests...\"\n"
        "    ./bin/photon_test_simple\n"
        "else\n"
        "    echo \"Build failed!\"\n"
        "fi\n"
    )
    
    # Make script executable
    execute_process(COMMAND chmod +x ${CMAKE_BINARY_DIR}/build_simple.sh)
endif()

# Summary
message(STATUS "Simplified build configuration complete.")
message(STATUS "To build and test:")
if(WIN32)
    message(STATUS "  Run: build_simple.bat")
else()
    message(STATUS "  Run: ./build_simple.sh")
endif()
message(STATUS "")
