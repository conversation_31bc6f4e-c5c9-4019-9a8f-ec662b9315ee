﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{8828A2B0-D0D1-342C-84C9-360CCF2649BC}"
	ProjectSection(ProjectDependencies) = postProject
		{7BC07066-DE55-388E-8ED1-D2365A52902E} = {7BC07066-DE55-388E-8ED1-D2365A52902E}
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC} = {4341BE95-3DBF-353B-8609-67EB9C3BEAAC}
		{B66BEC42-A149-332D-8267-55496F791598} = {B66BEC42-A149-332D-8267-55496F791598}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{3D06D26A-F294-3439-8214-FE6F28BA2805}"
	ProjectSection(ProjectDependencies) = postProject
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC} = {8828A2B0-D0D1-342C-84C9-360CCF2649BC}
		{7BC07066-DE55-388E-8ED1-D2365A52902E} = {7BC07066-DE55-388E-8ED1-D2365A52902E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{7BC07066-DE55-388E-8ED1-D2365A52902E}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "photon_core_simple", "photon_core_simple.vcxproj", "{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}"
	ProjectSection(ProjectDependencies) = postProject
		{7BC07066-DE55-388E-8ED1-D2365A52902E} = {7BC07066-DE55-388E-8ED1-D2365A52902E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "photon_test_simple", "photon_test_simple.vcxproj", "{B66BEC42-A149-332D-8267-55496F791598}"
	ProjectSection(ProjectDependencies) = postProject
		{7BC07066-DE55-388E-8ED1-D2365A52902E} = {7BC07066-DE55-388E-8ED1-D2365A52902E}
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC} = {4341BE95-3DBF-353B-8609-67EB9C3BEAAC}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.Debug|x64.ActiveCfg = Debug|x64
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.Debug|x64.Build.0 = Debug|x64
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.Release|x64.ActiveCfg = Release|x64
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.Release|x64.Build.0 = Release|x64
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8828A2B0-D0D1-342C-84C9-360CCF2649BC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{3D06D26A-F294-3439-8214-FE6F28BA2805}.Debug|x64.ActiveCfg = Debug|x64
		{3D06D26A-F294-3439-8214-FE6F28BA2805}.Release|x64.ActiveCfg = Release|x64
		{3D06D26A-F294-3439-8214-FE6F28BA2805}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3D06D26A-F294-3439-8214-FE6F28BA2805}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.Debug|x64.ActiveCfg = Debug|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.Debug|x64.Build.0 = Debug|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.Release|x64.ActiveCfg = Release|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.Release|x64.Build.0 = Release|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7BC07066-DE55-388E-8ED1-D2365A52902E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.Debug|x64.ActiveCfg = Debug|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.Debug|x64.Build.0 = Debug|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.Release|x64.ActiveCfg = Release|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.Release|x64.Build.0 = Release|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4341BE95-3DBF-353B-8609-67EB9C3BEAAC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B66BEC42-A149-332D-8267-55496F791598}.Debug|x64.ActiveCfg = Debug|x64
		{B66BEC42-A149-332D-8267-55496F791598}.Debug|x64.Build.0 = Debug|x64
		{B66BEC42-A149-332D-8267-55496F791598}.Release|x64.ActiveCfg = Release|x64
		{B66BEC42-A149-332D-8267-55496F791598}.Release|x64.Build.0 = Release|x64
		{B66BEC42-A149-332D-8267-55496F791598}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B66BEC42-A149-332D-8267-55496F791598}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B66BEC42-A149-332D-8267-55496F791598}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B66BEC42-A149-332D-8267-55496F791598}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F543432E-5AB9-373D-B43B-9535D5A6248D}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
