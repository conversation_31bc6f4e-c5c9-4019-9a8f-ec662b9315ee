// src/core/common.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Common definitions and temporary compatibility layer

#pragma once

// Windows math constants
#define _USE_MATH_DEFINES
#include <cmath>

#include <memory>
#include <vector>
#include <string>

// Real Embree include
#include <embree4/rtcore.h>

// Math constants (in case M_PI is not defined)
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// Forward declarations
class Scene;
class Camera;
class Integrator;
class Sampler;
class Material;
class Light;
class Mesh;
class Transform;
class Film;

// Common type aliases
using Color3 = class Vec3;
using Point3 = class Vec3;
using Normal3 = class Vec3;

// Global functions
RTCDevice getEmbreeDevice();

} // namespace photon
