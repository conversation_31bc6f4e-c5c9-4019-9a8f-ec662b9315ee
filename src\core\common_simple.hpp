// src/core/common_simple.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Common definitions for simplified build (without <PERSON><PERSON><PERSON>)

#pragma once

// Windows math constants
#define _USE_MATH_DEFINES
#include <cmath>

#include <memory>
#include <vector>
#include <string>
#include <limits>

// Include math classes
#include "math/vec3.hpp"

// Math constants (in case M_PI is not defined)
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace photon {

// Forward declarations
class Scene;
class Camera;
class Integrator;
class Sampler;
class Material;
class Light;
class Mesh;
class Transform;
class Film;

// Common type aliases
using Color3 = class Vec3;
using Point3 = class Vec3;
using Normal3 = class Vec3;

// Simplified intersection structure (without <PERSON>bree)
struct Intersection {
    bool hit = false;
    float t = std::numeric_limits<float>::infinity();
    Point3 point;
    Normal3 normal;
    Vec3 dpdu, dpdv;
    float u = 0, v = 0;
    std::shared_ptr<Material> material;
    
    Intersection() = default;
    
    bool isValid() const { return hit && t > 0; }
};

// Simplified ray structure
struct Ray {
    Point3 origin;
    Vec3 direction;
    float tMin = 1e-4f;
    float tMax = std::numeric_limits<float>::infinity();
    
    Ray() = default;
    Ray(const Point3& o, const Vec3& d) : origin(o), direction(d) {}
    Ray(const Point3& o, const Vec3& d, float tmin, float tmax) 
        : origin(o), direction(d), tMin(tmin), tMax(tmax) {}
    
    Point3 at(float t) const { return origin + t * direction; }
};

// Dummy RTCDevice type for simplified build
using RTCDevice = void*;

// Dummy functions for simplified build
inline RTCDevice getEmbreeDevice() { return nullptr; }

// Version information
#define PHOTON_VERSION_MAJOR 1
#define PHOTON_VERSION_MINOR 0
#define PHOTON_VERSION_PATCH 0
#define PHOTON_VERSION_STRING "1.0.0"

} // namespace photon
