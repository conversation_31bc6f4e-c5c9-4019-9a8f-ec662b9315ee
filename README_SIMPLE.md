# PhotonRender - Simplified Build Guide

## 🚀 Quick Start (Simplified Build)

Se hai problemi con la compilazione completa di Embree o vuoi testare rapidamente le funzionalità core di PhotonRender, puoi usare il build semplificato.

### ✅ **Requisiti Minimi**
- **Windows 10/11** con Visual Studio 2019/2022
- **CMake 3.20+**
- **Git** (per clonare il repository)

### 🔧 **Build Semplificato (Senza Embree)**

1. **Clona il repository:**
   ```bash
   git clone https://github.com/Ilmazza/photon-render.git
   cd photon-render
   ```

2. **Esegui il build semplificato:**
   ```bash
   build_simple.bat
   ```

3. **Risultato:**
   - ✅ Compila solo i componenti core
   - ✅ Testa tutte le funzionalità base
   - ✅ Genera immagini di test
   - ✅ Crea report dettagliato

### 📋 **Cosa Include il Build Semplificato**

#### ✅ **Funzionalità Incluse:**
- **Math Library**: Vec3, Matrix4, <PERSON>, Intersection
- **Scene Loading**: JSON parser, scene validation
- **Mesh Loading**: OBJ parser, test mesh generation
- **Image I/O**: PNG, JPEG, BMP export (con STB)
- **Mock Renderer**: Rendering di test senza ray tracing
- **Test Suite**: Suite completa di test automatici

#### ❌ **Funzionalità Escluse:**
- **Intel Embree**: BVH acceleration
- **Real Ray Tracing**: Path tracing reale
- **CUDA/OptiX**: GPU acceleration
- **Advanced Materials**: PBR, volumetrics

### 🎯 **Output del Build**

Dopo il build semplificato, troverai:

```
build_simple/
├── bin/Release/photon_test_simple.exe    # Eseguibile di test
├── test_report.md                        # Report dettagliato
├── photon_test_render.png               # Render di test Cornell Box
├── test_image_io.png                    # Test I/O immagini
└── test_mock_render.png                 # Test mock renderer
```

### 📊 **Test Automatici**

Il build semplificato esegue automaticamente:

1. **Math Library Test**: Verifica operazioni matematiche
2. **Scene Loading Test**: Test caricamento scene JSON
3. **Mesh Loading Test**: Test caricamento mesh OBJ
4. **Image I/O Test**: Test export immagini
5. **Mock Rendering Test**: Test rendering semplificato

### 🔍 **Esempio di Output**

```
=== PhotonRender Test Suite ===
✓ Math Library - All math operations working (2.1ms)
✓ Scene Loading - Scene creation and validation working (15.3ms)
✓ Mesh Loading - Mesh creation and validation working (8.7ms)
✓ Image I/O - Image creation and saving working (45.2ms)
✓ Mock Rendering - Mock rendering and saving working (234.8ms)

Summary: 5/5 tests passed

🎉 All tests passed! PhotonRender is working correctly.
```

### 🖼️ **Immagini Generate**

Il sistema genera automaticamente diverse immagini di test:

1. **Cornell Box**: Simulazione semplificata della famosa Cornell Box
2. **Gradient Test**: Test dei gradienti colore
3. **Checkerboard**: Pattern a scacchiera per test geometria
4. **Sphere Test**: Test rendering sfere con shading

### 🛠️ **Sviluppo e Debug**

Per sviluppatori che vogliono lavorare sul codice:

```bash
# Build manuale
cd build_simple
cmake .. -f ../CMakeLists_simple.txt -G "Visual Studio 16 2019" -A x64
cmake --build . --config Release

# Solo test
bin\Release\photon_test_simple.exe

# Debug build
cmake --build . --config Debug
```

### 📈 **Performance**

Il build semplificato è ottimizzato per:
- **Compilazione rapida**: ~30 secondi vs 10+ minuti
- **Test veloci**: Tutti i test in <5 secondi
- **Footprint ridotto**: ~50MB vs 500MB+
- **Dipendenze minime**: Solo STB per I/O immagini

### 🔄 **Passaggio al Build Completo**

Quando sei pronto per il build completo con Embree:

1. **Installa dipendenze complete**:
   - Intel Embree 4.3.3
   - Intel TBB
   - CUDA Toolkit (opzionale)

2. **Usa il CMakeLists.txt principale**:
   ```bash
   mkdir build
   cd build
   cmake .. -G "Visual Studio 16 2019" -A x64
   cmake --build . --config Release
   ```

### ❓ **Troubleshooting**

#### **Problema: CMake non trovato**
```bash
# Installa CMake da https://cmake.org/download/
# Aggiungi al PATH di sistema
```

#### **Problema: Visual Studio non trovato**
```bash
# Installa Visual Studio 2019/2022 con C++ tools
# Oppure usa Visual Studio Build Tools
```

#### **Problema: STB non trovato**
```bash
# STB è incluso nel repository
# Se manca, scarica da https://github.com/nothings/stb
```

### 📞 **Supporto**

Per problemi con il build semplificato:
1. Controlla i prerequisiti
2. Verifica il log di build
3. Consulta il test report generato
4. Apri un issue su GitHub

---

**Il build semplificato ti permette di testare e sviluppare PhotonRender senza le complessità del build completo!**
