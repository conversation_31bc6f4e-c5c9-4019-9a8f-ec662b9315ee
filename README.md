# 🚀 PhotonRender - Professional Rendering Engine for SketchUp

<div align="center">

![PhotonRender Logo](https://img.shields.io/badge/PhotonRender-v0.1.0-blue?style=for-the-badge&logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=)

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![C++](https://img.shields.io/badge/C++-17-blue.svg?style=flat&logo=c%2B%2B)](https://isocpp.org/)
[![Ruby](https://img.shields.io/badge/Ruby-2.7+-red.svg?style=flat&logo=ruby)](https://www.ruby-lang.org/)
[![CUDA](https://img.shields.io/badge/CUDA-11.0+-green.svg?style=flat&logo=nvidia)](https://developer.nvidia.com/cuda-zone)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](https://github.com/yourusername/photon-render)

**Un motore di rendering fotorealistico ad alte prestazioni per SketchUp con capacità di rendering ibrido CPU/GPU**

[🎯 Features](#-features) • [🚀 Quick Start](#-quick-start) • [📖 Documentation](#-documentation) • [🤝 Contributing](#-contributing)

</div>

---

## 🎯 Features

### 🎨 **Rendering Avanzato**
- **Path Tracing Fisicamente Accurato** con Multiple Importance Sampling
- **Disney BRDF** per materiali realistici
- **Global Illumination** con caustics e subsurface scattering
- **HDRI Environment Lighting** per illuminazione naturale

### ⚡ **Performance Ottimizzate**
- **GPU Acceleration** con CUDA/OptiX per hardware RTX
- **Intel Embree 4** per ray-tracing CPU ottimizzato
- **Tile-based Parallel Rendering** con Intel TBB
- **AI-Powered Denoising** per risultati puliti con meno campioni

### 🔧 **Integrazione SketchUp**
- **Plugin Ruby Nativo** con interfaccia intuitiva
- **Real-time Viewport Preview** durante il rendering
- **Automatic Scene Export** da geometria SketchUp
- **Material Conversion** automatica per workflow seamless

### 🛠️ **Developer-Friendly**
- **Modern C++17** con architettura modulare
- **Cross-Platform** (Windows, macOS, Linux)
- **Comprehensive Testing** con unit e integration tests
- **Professional Documentation** con esempi completi

---

## 🚀 Quick Start

### 📋 Prerequisiti

```bash
# Compilatore C++17
- GCC 9+ / Clang 10+ / MSVC 2019+

# Build System
- CMake 3.20+

# Dipendenze (automatiche)
- Intel Embree 4.2+
- Intel TBB 2021.9+
- Eigen 3.4+

# Opzionali
- CUDA 11.0+ (per GPU acceleration)
- Ruby 2.7+ (per SketchUp plugin)
```

### ⚙️ Installazione

```bash
# 1. Clone del repository
git clone https://github.com/yourusername/photon-render.git
cd photon-render

# 2. Setup automatico ambiente
./scripts/setup_dev.sh

# 3. Build del progetto
cd build
cmake --build . --config Release -j$(nproc)

# 4. Test dell'installazione
./bin/photon_render --scene=../tests/scenes/cornell_box.json
```

### 🎮 Primo Render

```cpp
#include "photon/renderer.hpp"

int main() {
    // Crea renderer
    auto renderer = std::make_unique<photon::Renderer>();
    
    // Configura scene
    auto scene = std::make_shared<photon::Scene>();
    scene->loadFromFile("cornell_box.scene");
    
    // Setup camera
    auto camera = std::make_shared<photon::PerspectiveCamera>(
        photon::Vec3(0, 1, 3),    // position
        photon::Vec3(0, 1, 0),    // target  
        45.0f                     // fov
    );
    
    // Configura settings
    photon::RenderSettings settings;
    settings.width = 1920;
    settings.height = 1080;
    settings.samplesPerPixel = 100;
    
    // Avvia rendering
    renderer->setScene(scene);
    renderer->setCamera(camera);
    renderer->setSettings(settings);
    renderer->render();
    
    return 0;
}
```

---

## 📁 Struttura Progetto

```
photon-render/
├── 🔧 CMakeLists.txt              # Build configuration
├── 📖 README.md                   # Questo file
├── 📄 LICENSE                     # Apache 2.0 License
│
├── 📁 src/                        # Codice sorgente
│   ├── 📁 core/                   # C++ rendering engine
│   │   ├── 📁 math/               # Vector/Matrix math
│   │   ├── 📁 scene/              # Scene management
│   │   ├── 📁 material/           # Material system
│   │   ├── 📁 integrator/         # Rendering algorithms
│   │   └── 📁 renderer.{hpp,cpp}  # Main renderer
│   │
│   ├── 📁 gpu/                    # GPU kernels
│   │   ├── 📁 cuda/               # NVIDIA CUDA
│   │   └── 📁 shaders/            # Compute shaders
│   │
│   ├── 📁 ruby/                   # SketchUp plugin
│   │   └── 📁 photon_render/      # Plugin components
│   │
│   └── 📁 bindings/               # Ruby-C++ bridge
│
├── 📁 tests/                      # Test suite
│   ├── 📁 unit/                   # Unit tests
│   ├── 📁 integration/            # Integration tests
│   └── 📁 scenes/                 # Test scenes
│
├── 📁 docs/                       # Documentazione
│   ├── 📄 app_map.md              # Mappa applicazione
│   ├── 📄 technical-guide.md      # Guida tecnica
│   └── 📄 project-structure.md    # Struttura dettagliata
│
├── 📁 scripts/                    # Build & utility scripts
│   ├── 🔧 setup_dev.sh            # Setup ambiente
│   └── 🧪 test_and_deploy.py      # Testing & deployment
│
└── 📁 assets/                     # Risorse test
    ├── 📁 hdri/                   # Environment maps
    ├── 📁 textures/               # Test textures
    └── 📁 models/                 # Test models
```

---

## 🎯 Roadmap

### 📅 **Milestone 1: "First Light"** (Mese 1)
- [x] ✅ Architettura base e setup
- [x] ✅ Integrazione Embree
- [ ] 🔄 Basic ray tracing
- [ ] 🔄 SketchUp plugin base

### 📅 **Milestone 2: "Material World"** (Mese 2)  
- [ ] 📋 Sistema materiali PBR
- [ ] 📋 Disney BRDF implementation
- [ ] 📋 Texture mapping
- [ ] 📋 UI settings completa

### 📅 **Milestone 3: "Need for Speed"** (Mese 3)
- [ ] 📋 GPU acceleration (CUDA/OptiX)
- [ ] 📋 AI denoising
- [ ] 📋 Performance optimization
- [ ] 📋 Benchmark suite

### 📅 **Milestone 4: "Production Ready"** (Mese 4)
- [ ] 📋 Advanced features (volumetrics, caustics)
- [ ] 📋 Animation support
- [ ] 📋 Distributed rendering
- [ ] 📋 Extension Warehouse release

---

## 📖 Documentation

- 📚 **[Technical Guide](docs/technical-guide.md)** - Guida tecnica completa
- 🗺️ **[Application Map](docs/app_map.md)** - Mappa dell'applicazione  
- 🏗️ **[Project Structure](docs/project-structure.md)** - Struttura dettagliata
- 🔧 **[API Reference](docs/api/)** - Documentazione API (generata)
- 🎓 **[User Manual](docs/user_manual.md)** - Manuale utente

---

## 🤝 Contributing

Contributi benvenuti! Leggi la [Contributing Guide](CONTRIBUTING.md) per iniziare.

### 🐛 Bug Reports
Usa [GitHub Issues](https://github.com/yourusername/photon-render/issues) per segnalare bug.

### 💡 Feature Requests  
Proponi nuove funzionalità tramite [GitHub Discussions](https://github.com/yourusername/photon-render/discussions).

### 🔧 Development
```bash
# Setup ambiente di sviluppo
./scripts/setup_dev.sh

# Esegui tests
cd build && ctest

# Format codice
clang-format -i src/**/*.{cpp,hpp}
```

---

## 📄 License

Questo progetto è rilasciato sotto [Apache License 2.0](LICENSE).

---

## 🙏 Acknowledgments

- **[Intel Embree](https://embree.github.io/)** - High-performance ray tracing
- **[Intel TBB](https://github.com/oneapi-src/oneTBB)** - Threading Building Blocks
- **[NVIDIA OptiX](https://developer.nvidia.com/optix)** - GPU ray tracing
- **[SketchUp SDK](https://developer.sketchup.com/)** - 3D modeling platform

---

<div align="center">

**⭐ Se ti piace PhotonRender, lascia una stella su GitHub! ⭐**

[🌟 Star this repo](https://github.com/yourusername/photon-render) • [🐦 Follow on Twitter](https://twitter.com/yourusername) • [💬 Join Discord](https://discord.gg/yourinvite)

</div>
