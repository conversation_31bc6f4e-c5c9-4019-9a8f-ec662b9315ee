// src/core/geometry/mesh.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Mesh geometry representation

#pragma once

#include "../common.hpp"
#include "../math/vec3.hpp"
#include <vector>
#include <memory>
#include <string>

namespace photon {

/**
 * @brief Vertex structure for mesh geometry
 */
struct Vertex {
    Point3 position;        ///< Vertex position
    Normal3 normal;         ///< Vertex normal
    Vec3 tangent;          ///< Vertex tangent
    Vec3 bitangent;        ///< Vertex bitangent
    float u = 0.0f;        ///< Texture coordinate U
    float v = 0.0f;        ///< Texture coordinate V
    
    Vertex() = default;
    Vertex(const Point3& pos) : position(pos) {}
    Vertex(const Point3& pos, const Normal3& norm) : position(pos), normal(norm) {}
    Vertex(const Point3& pos, const Normal3& norm, float texU, float texV) 
        : position(pos), normal(norm), u(texU), v(texV) {}
};

/**
 * @brief Triangle face structure
 */
struct Triangle {
    uint32_t v0, v1, v2;   ///< Vertex indices
    uint32_t materialId = 0; ///< Material ID
    
    Triangle() = default;
    Triangle(uint32_t i0, uint32_t i1, uint32_t i2) : v0(i0), v1(i1), v2(i2) {}
    Triangle(uint32_t i0, uint32_t i1, uint32_t i2, uint32_t matId) 
        : v0(i0), v1(i1), v2(i2), materialId(matId) {}
};

/**
 * @brief Mesh bounding box
 */
struct BoundingBox {
    Point3 min;
    Point3 max;
    
    BoundingBox() : min(1e30f), max(-1e30f) {}
    BoundingBox(const Point3& minPt, const Point3& maxPt) : min(minPt), max(maxPt) {}
    
    void expand(const Point3& point) {
        min = Point3(std::min(min.x, point.x), std::min(min.y, point.y), std::min(min.z, point.z));
        max = Point3(std::max(max.x, point.x), std::max(max.y, point.y), std::max(max.z, point.z));
    }
    
    Point3 center() const { return (min + max) * 0.5f; }
    Vec3 size() const { return max - min; }
    float volume() const { 
        Vec3 s = size(); 
        return s.x * s.y * s.z; 
    }
    bool isEmpty() const { return min.x > max.x || min.y > max.y || min.z > max.z; }
};

/**
 * @brief Mesh geometry class
 */
class Mesh {
public:
    /**
     * @brief Default constructor
     */
    Mesh() = default;
    
    /**
     * @brief Constructor with name
     */
    explicit Mesh(const std::string& name) : name_(name) {}
    
    /**
     * @brief Destructor
     */
    ~Mesh() = default;
    
    // Vertex operations
    void addVertex(const Vertex& vertex);
    void addVertex(const Point3& position, const Normal3& normal = Normal3(0, 1, 0));
    void addVertex(const Point3& position, const Normal3& normal, float u, float v);
    
    // Triangle operations
    void addTriangle(const Triangle& triangle);
    void addTriangle(uint32_t v0, uint32_t v1, uint32_t v2, uint32_t materialId = 0);
    
    // Accessors
    const std::vector<Vertex>& getVertices() const { return vertices_; }
    const std::vector<Triangle>& getTriangles() const { return triangles_; }
    std::vector<Vertex>& getVertices() { return vertices_; }
    std::vector<Triangle>& getTriangles() { return triangles_; }
    
    size_t getVertexCount() const { return vertices_.size(); }
    size_t getTriangleCount() const { return triangles_.size(); }
    
    const std::string& getName() const { return name_; }
    void setName(const std::string& name) { name_ = name; }
    
    // Geometry operations
    void computeNormals();
    void computeTangents();
    void computeBoundingBox();
    const BoundingBox& getBoundingBox() const { return boundingBox_; }
    
    // Utility functions
    void clear();
    bool isEmpty() const { return vertices_.empty() || triangles_.empty(); }
    void reserve(size_t vertexCount, size_t triangleCount);
    
    // Transform operations
    void transform(const Matrix4& matrix);
    void translate(const Vec3& translation);
    void scale(float factor);
    void scale(const Vec3& factors);
    
    // Validation
    bool validate() const;
    void fixWindingOrder();
    
    // Statistics
    struct MeshStats {
        size_t vertexCount = 0;
        size_t triangleCount = 0;
        size_t materialCount = 0;
        BoundingBox boundingBox;
        float surfaceArea = 0.0f;
        bool hasNormals = false;
        bool hasTexCoords = false;
        bool hasTangents = false;
    };
    
    MeshStats getStats() const;
    void printStats() const;

private:
    std::string name_;                    ///< Mesh name
    std::vector<Vertex> vertices_;        ///< Vertex data
    std::vector<Triangle> triangles_;     ///< Triangle indices
    BoundingBox boundingBox_;             ///< Mesh bounding box
    bool boundingBoxValid_ = false;       ///< Bounding box validity flag
    
    // Helper functions
    Normal3 computeFaceNormal(const Triangle& triangle) const;
    float computeTriangleArea(const Triangle& triangle) const;
};

/**
 * @brief Mesh utilities
 */
class MeshUtils {
public:
    // Primitive generation
    static std::shared_ptr<Mesh> createCube(float size = 1.0f);
    static std::shared_ptr<Mesh> createSphere(float radius = 1.0f, int segments = 32);
    static std::shared_ptr<Mesh> createPlane(float width = 1.0f, float height = 1.0f);
    static std::shared_ptr<Mesh> createCylinder(float radius = 1.0f, float height = 1.0f, int segments = 32);
    
    // Mesh operations
    static std::shared_ptr<Mesh> merge(const std::vector<std::shared_ptr<Mesh>>& meshes);
    static std::shared_ptr<Mesh> subdivide(const Mesh& mesh, int levels = 1);
    static std::shared_ptr<Mesh> smooth(const Mesh& mesh, float factor = 0.5f);
    
    // Analysis
    static bool isManifold(const Mesh& mesh);
    static bool isClosed(const Mesh& mesh);
    static std::vector<std::vector<uint32_t>> findConnectedComponents(const Mesh& mesh);
};

} // namespace photon
