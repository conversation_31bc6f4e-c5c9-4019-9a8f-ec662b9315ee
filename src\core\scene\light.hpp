// src/core/scene/light.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Light source system

#pragma once

#include "../math/vec3.hpp"
#include "../math/ray.hpp"
#include <memory>
#include <string>

namespace photon {

// Forward declarations
class Scene;
class Sampler;
struct Intersection;

/**
 * @brief Light sample structure
 */
struct LightSample {
    Color3 Li;          ///< Incident radiance
    Vec3 wi;            ///< Direction to light (from surface)
    float pdf;          ///< Probability density function
    float distance;     ///< Distance to light source
    bool isDelta;       ///< Is delta light (point/directional)
    
    LightSample() : Li(0), wi(0), pdf(0), distance(0), isDelta(false) {}
    LightSample(const Color3& Li, const Vec3& wi, float pdf, float distance, bool isDelta = false)
        : Li(Li), wi(wi), pdf(pdf), distance(distance), isDelta(isDelta) {}
    
    bool isValid() const { return pdf > 0.0f && !Li.isZero(); }
};

/**
 * @brief Abstract base light class
 */
class Light {
public:
    Light() = default;
    virtual ~Light() = default;
    
    // Non-copyable
    Light(const Light&) = delete;
    Light& operator=(const Light&) = delete;
    
    /**
     * @brief Sample incident illumination at a point
     * 
     * @param isect Surface intersection point
     * @param sampler Random sampler
     * @return Light sample
     */
    virtual LightSample sample(const Intersection& isect, Sampler& sampler) const = 0;
    
    /**
     * @brief Evaluate incident illumination for given direction
     * 
     * @param isect Surface intersection point
     * @param wi Direction to light
     * @return Incident radiance
     */
    virtual Color3 Li(const Intersection& isect, const Vec3& wi) const = 0;
    
    /**
     * @brief Get PDF for sampling given direction
     * 
     * @param isect Surface intersection point
     * @param wi Direction to light
     * @return Probability density
     */
    virtual float pdf(const Intersection& isect, const Vec3& wi) const = 0;
    
    /**
     * @brief Get total power emitted by light
     */
    virtual Color3 power() const = 0;
    
    /**
     * @brief Check if light is delta (point/directional)
     */
    virtual bool isDelta() const = 0;
    
    /**
     * @brief Get light name
     */
    virtual std::string getName() const = 0;
    
    /**
     * @brief Preprocess light (called once before rendering)
     */
    virtual void preprocess(const Scene& scene) {}
};

/**
 * @brief Point light source
 */
class PointLight : public Light {
public:
    /**
     * @brief Constructor
     * 
     * @param position Light position
     * @param intensity Light intensity (power per solid angle)
     */
    PointLight(const Point3& position, const Color3& intensity);
    
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return true; }
    std::string getName() const override { return "Point"; }
    
    /**
     * @brief Set position
     */
    void setPosition(const Point3& position) { m_position = position; }
    
    /**
     * @brief Get position
     */
    const Point3& getPosition() const { return m_position; }
    
    /**
     * @brief Set intensity
     */
    void setIntensity(const Color3& intensity) { m_intensity = intensity; }
    
    /**
     * @brief Get intensity
     */
    const Color3& getIntensity() const { return m_intensity; }

private:
    Point3 m_position;
    Color3 m_intensity;
};

/**
 * @brief Directional light source (sun)
 */
class DirectionalLight : public Light {
public:
    /**
     * @brief Constructor
     * 
     * @param direction Light direction (towards surface)
     * @param irradiance Irradiance (power per area)
     */
    DirectionalLight(const Vec3& direction, const Color3& irradiance);
    
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return true; }
    std::string getName() const override { return "Directional"; }

private:
    Vec3 m_direction;
    Color3 m_irradiance;
    float m_worldRadius; // Set during preprocessing
};

/**
 * @brief Area light source
 */
class AreaLight : public Light {
public:
    /**
     * @brief Constructor
     * 
     * @param emission Emitted radiance
     * @param area Light area
     */
    AreaLight(const Color3& emission, float area);
    
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return false; }
    std::string getName() const override { return "Area"; }
    
    /**
     * @brief Sample point on light surface
     */
    virtual Point3 sampleSurface(Sampler& sampler) const = 0;
    
    /**
     * @brief Get surface normal at point
     */
    virtual Normal3 getNormal(const Point3& point) const = 0;
    
    /**
     * @brief Get surface area
     */
    float getArea() const { return m_area; }

protected:
    Color3 m_emission;
    float m_area;
};

/**
 * @brief Environment light (HDRI)
 */
class EnvironmentLight : public Light {
public:
    /**
     * @brief Constructor
     * 
     * @param emission Constant emission color
     */
    EnvironmentLight(const Color3& emission = Color3(0.1f));
    
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return false; }
    std::string getName() const override { return "Environment"; }
    
    /**
     * @brief Evaluate environment map
     */
    virtual Color3 evaluate(const Vec3& direction) const;

private:
    Color3 m_emission;
    float m_worldRadius; // Set during preprocessing
};

/**
 * @brief Spot light source
 */
class SpotLight : public Light {
public:
    /**
     * @brief Constructor
     * 
     * @param position Light position
     * @param direction Light direction
     * @param intensity Light intensity
     * @param innerAngle Inner cone angle (radians)
     * @param outerAngle Outer cone angle (radians)
     */
    SpotLight(const Point3& position, const Vec3& direction, const Color3& intensity,
             float innerAngle, float outerAngle);
    
    LightSample sample(const Intersection& isect, Sampler& sampler) const override;
    Color3 Li(const Intersection& isect, const Vec3& wi) const override;
    float pdf(const Intersection& isect, const Vec3& wi) const override;
    Color3 power() const override;
    bool isDelta() const override { return true; }
    std::string getName() const override { return "Spot"; }

private:
    Point3 m_position;
    Vec3 m_direction;
    Color3 m_intensity;
    float m_innerAngle;
    float m_outerAngle;
    float m_cosInner;
    float m_cosOuter;
    
    /**
     * @brief Compute falloff factor
     */
    float falloff(const Vec3& wi) const;
};

} // namespace photon
