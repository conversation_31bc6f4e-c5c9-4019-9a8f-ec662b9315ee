// src/core/renderer.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Main renderer implementation

#include "renderer.hpp"
#include "sampler/sampler.hpp"
#include "image/image_io.hpp"
#include <chrono>
#include <iostream>

namespace photon {

Renderer::Renderer() {
    initializeEmbree();
}

Renderer::~Renderer() {
    // Device is managed globally, don't release here
}

void Renderer::initializeEmbree() {
    // Get global Embree device
    m_device = getEmbreeDevice();
    if (!m_device) {
        std::cerr << "Embree device not initialized. Call photon::initialize() first." << std::endl;
        throw std::runtime_error("Embree initialization failed");
    }
}

void Renderer::setScene(std::shared_ptr<Scene> scene) {
    m_scene = scene;
    if (m_scene) {
        buildAccelerationStructure();
    }
}

void Renderer::setCamera(std::shared_ptr<Camera> camera) {
    m_camera = camera;
}

void Renderer::setIntegrator(std::shared_ptr<Integrator> integrator) {
    m_integrator = integrator;
}

void Renderer::setSettings(const RenderSettings& settings) {
    m_settings = settings;
    m_film = std::make_unique<Film>(settings.width, settings.height);
}

void Renderer::buildAccelerationStructure() {
    if (!m_scene) return;
    
    // Build Embree BVH
    RTCScene rtcScene = rtcNewScene(m_device);
    
    // Add geometry to Embree scene
    for (const auto& mesh : m_scene->getMeshes()) {
        RTCGeometry geom = rtcNewGeometry(m_device, RTC_GEOMETRY_TYPE_TRIANGLE);
        
        // Set vertex buffer
        rtcSetSharedGeometryBuffer(geom, RTC_BUFFER_TYPE_VERTEX, 0,
            RTC_FORMAT_FLOAT3, mesh->vertices.data(), 0,
            sizeof(Vec3), mesh->vertices.size());
        
        // Set index buffer
        rtcSetSharedGeometryBuffer(geom, RTC_BUFFER_TYPE_INDEX, 0,
            RTC_FORMAT_UINT3, mesh->indices.data(), 0,
            sizeof(unsigned int) * 3, mesh->indices.size() / 3);
        
        rtcCommitGeometry(geom);
        rtcAttachGeometry(rtcScene, geom);
        rtcReleaseGeometry(geom);
    }
    
    rtcCommitScene(rtcScene);
    m_scene->setEmbreeScene(rtcScene);
}

void Renderer::render() {
    if (!m_scene || !m_camera || !m_integrator || !m_film) {
        throw std::runtime_error("Renderer not properly configured");
    }
    
    m_isRendering = true;
    m_shouldStop = false;

    // Reset stats
    m_stats.renderedTiles = 0;
    m_stats.totalTiles = 0;
    m_stats.totalSamples = 0;
    m_stats.renderTime = 0.0f;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Calculate tiles
    int tilesX = (m_settings.width + m_settings.tileSize - 1) / m_settings.tileSize;
    int tilesY = (m_settings.height + m_settings.tileSize - 1) / m_settings.tileSize;
    m_stats.totalTiles = tilesX * tilesY;
    
    // Render tiles in parallel
    tbb::parallel_for(
        tbb::blocked_range2d<int>(0, tilesY, 1, 0, tilesX, 1),
        [this](const tbb::blocked_range2d<int>& range) {
            for (int ty = range.rows().begin(); ty < range.rows().end(); ++ty) {
                for (int tx = range.cols().begin(); tx < range.cols().end(); ++tx) {
                    if (m_shouldStop) return;
                    renderTile(tx, ty);
                }
            }
        }
    );
    
    auto endTime = std::chrono::high_resolution_clock::now();
    m_stats.renderTime = std::chrono::duration<float>(endTime - startTime).count();
    
    m_isRendering = false;
    
    // Final progress callback
    if (m_progressCallback) {
        m_progressCallback(1.0f, m_stats);
    }
}

void Renderer::renderTile(int tileX, int tileY) {
    int x0 = tileX * m_settings.tileSize;
    int y0 = tileY * m_settings.tileSize;
    int x1 = std::min(x0 + m_settings.tileSize, m_settings.width);
    int y1 = std::min(y0 + m_settings.tileSize, m_settings.height);
    
    renderTileInternal(x0, y0, x1 - x0, y1 - y0);
    
    m_stats.renderedTiles++;
    
    // Progress callback
    if (m_progressCallback) {
        m_progressCallback(m_stats.getProgress(), m_stats);
    }
    
    // Tile callback with pixel data
    if (m_tileCallback) {
        std::vector<float> tilePixels((x1 - x0) * (y1 - y0) * 3);
        for (int y = y0; y < y1; ++y) {
            for (int x = x0; x < x1; ++x) {
                Color3 pixel = m_film->getPixel(x, y);
                int idx = ((y - y0) * (x1 - x0) + (x - x0)) * 3;
                tilePixels[idx + 0] = pixel.x;
                tilePixels[idx + 1] = pixel.y;
                tilePixels[idx + 2] = pixel.z;
            }
        }
        m_tileCallback(x0, y0, x1 - x0, y1 - y0, tilePixels.data());
    }
}

void Renderer::renderTileInternal(int x0, int y0, int width, int height) {
    // Create sampler for this tile
    auto sampler = std::make_unique<RandomSampler>(x0 + y0 * m_settings.width);

    // Render each pixel in tile
    for (int y = y0; y < y0 + height; ++y) {
        for (int x = x0; x < x0 + width; ++x) {
            // Multiple samples per pixel
            Color3 pixelColor(0);
            for (int s = 0; s < m_settings.samplesPerPixel; ++s) {
                pixelColor += samplePixel(x, y, *sampler);
                m_stats.totalSamples++;
            }
            pixelColor /= float(m_settings.samplesPerPixel);

            // Store in film
            m_film->addSample(x, y, pixelColor);
        }
    }
}

Color3 Renderer::samplePixel(int x, int y, Sampler& sampler) {
    // Generate camera ray
    float u = (x + sampler.get1D()) / float(m_settings.width);
    float v = (y + sampler.get1D()) / float(m_settings.height);
    Ray ray = m_camera->generateRay(u, v, sampler);
    
    // Trace ray through scene
    return m_integrator->Li(ray, *m_scene, sampler);
}

void Renderer::stop() {
    m_shouldStop = true;
}

bool Renderer::saveImage(const std::string& filename, int quality) const {
    if (!m_film) {
        std::cerr << "Error: No film data to save" << std::endl;
        return false;
    }

    return ImageIO::saveFromFilm(filename, *m_film, quality);
}

} // namespace photon
