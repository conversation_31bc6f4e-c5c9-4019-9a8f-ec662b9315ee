// src/core/photon_api.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Main API implementation

#include "../../include/photon/photon.hpp"
#include "common.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <unordered_map>

namespace photon {

// Global state
static bool g_initialized = false;
static RTCDevice g_device = nullptr;
static LogLevel g_logLevel = LogLevel::Info;
static std::function<void(LogLevel, const std::string&)> g_logCallback;

// Default log callback
void defaultLogCallback(LogLevel level, const std::string& message) {
    const char* levelStr[] = {"DEBUG", "INFO", "WARNING", "ERROR", "FATAL"};
    std::cout << "[" << levelStr[static_cast<int>(level)] << "] " << message << std::endl;
}

// Embree error callback
void embreeErrorCallback(void* userPtr, RTCError error, const char* str) {
    std::string message = "Embree Error: " + std::string(str);
    if (g_logCallback) {
        g_logCallback(LogLevel::Error, message);
    } else {
        defaultLogCallback(LogLevel::Error, message);
    }
}

bool initialize() {
    if (g_initialized) {
        return true;
    }
    
    // Set default log callback
    if (!g_logCallback) {
        g_logCallback = defaultLogCallback;
    }
    
    g_logCallback(LogLevel::Info, "Initializing PhotonRender v" PHOTON_VERSION_STRING);
    
    // Initialize Embree
    g_device = rtcNewDevice(nullptr);
    if (!g_device) {
        g_logCallback(LogLevel::Error, "Failed to initialize Embree device");
        return false;
    }
    
    // Set Embree error callback
    rtcSetDeviceErrorFunction(g_device, embreeErrorCallback, nullptr);
    
    g_logCallback(LogLevel::Info, "Embree device initialized successfully");
    g_logCallback(LogLevel::Info, "PhotonRender initialization complete");
    
    g_initialized = true;
    return true;
}

void shutdown() {
    if (!g_initialized) {
        return;
    }
    
    g_logCallback(LogLevel::Info, "Shutting down PhotonRender");
    
    // Release Embree device
    if (g_device) {
        rtcReleaseDevice(g_device);
        g_device = nullptr;
    }
    
    g_initialized = false;
    g_logCallback(LogLevel::Info, "PhotonRender shutdown complete");
}

Version getVersion() {
    Version version;
    version.major = PHOTON_VERSION_MAJOR;
    version.minor = PHOTON_VERSION_MINOR;
    version.patch = PHOTON_VERSION_PATCH;
    version.string = PHOTON_VERSION_STRING;
    return version;
}

bool hasGPUAcceleration() {
    // TODO: Implement CUDA/OptiX detection
    return false;
}

std::vector<GPUDevice> getGPUDevices() {
    // TODO: Implement GPU device enumeration
    return {};
}

Capabilities getCapabilities() {
    Capabilities caps;
    caps.hasEmbree = (g_device != nullptr);
    caps.hasCUDA = false;  // TODO: Implement CUDA detection
    caps.hasOptiX = false; // TODO: Implement OptiX detection
    caps.hasOpenMP = true; // Enabled in CMake
    caps.hasTBB = true;    // We use TBB
    caps.maxThreads = std::thread::hardware_concurrency();
    caps.gpuCount = 0;     // TODO: Implement GPU counting
    return caps;
}

void setLogLevel(LogLevel level) {
    g_logLevel = level;
}

void setLogCallback(std::function<void(LogLevel, const std::string&)> callback) {
    g_logCallback = callback;
}

// Profiler implementation (simple stub for now)
static std::unordered_map<std::string, std::chrono::high_resolution_clock::time_point> g_profileTimes;

void Profiler::begin(const std::string& name) {
    g_profileTimes[name] = std::chrono::high_resolution_clock::now();
}

void Profiler::end(const std::string& name) {
    auto it = g_profileTimes.find(name);
    if (it != g_profileTimes.end()) {
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - it->second);
        // Store timing data (for now just remove from map)
        g_profileTimes.erase(it);
    }
}

void Profiler::reset() {
    g_profileTimes.clear();
}

std::string Profiler::getReport() {
    return "Profiler report not yet implemented";
}

// Global device accessor for internal use
RTCDevice getEmbreeDevice() {
    return g_device;
}

} // namespace photon
